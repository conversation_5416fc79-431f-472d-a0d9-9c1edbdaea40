import Taro from '@tarojs/taro'
import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { activePathState } from '@/store/global'
import './ConpouItem.scss'

const ConputItem = () => {
  const activePath = useObjAtom(activePathState)
  return (
    <>
      <div className="coupon">
        <div className="couponItem">
          <div className="leftItem">
            <div className="count">x200</div>
            <div className="content">
              <div className="moneyCount">
                3<span className="unit">元</span>
              </div>
              <div className="xianzhi">无门槛</div>
            </div>
          </div>
          <div className="rightItem">
            <div className="leftContentBox">
              <div className="title">无门槛代金券</div>
              <div className="time">
                23 : 59 : 59 <span className="timeText">后过期</span>
              </div>
              <div className="useTitle">全店可用</div>
            </div>
            <div className="rightBtnBox">
              <div className="btns">
                <div
                  className="btn snedFri"
                  onClick={() =>
                    Taro.navigateTo({
                      url: '/pages/sendFriend/index'
                    })
                  }
                >
                  送好友
                </div>
                <div
                  className="btn toUse"
                  onClick={() => {
                    Taro.switchTab({
                      url: '/pages/inspiration/index',
                      success: () => {
                        activePath.set('/pages/inspiration/index')
                      }
                    })
                  }}
                >
                  去使用
                </div>
              </div>
              {/* <div className="title">待领取</div> */}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ConputItem
