{"name": "ai-mp-wode-shop", "version": "1.0.0", "private": true, "description": "ARTIN · 爱定制", "templateInfo": {"name": "default", "typescript": true, "css": "sass", "framework": "React"}, "scripts": {"prepare": "husky install", "postinstall": "weapp-tw patch", "dev": "npm run build:test -- --watch", "build:test": "taro build --type weapp --mode test", "build:live": "taro build --type weapp --mode live", "dev:h5": "npm run build:h5:test -- --watch", "build:h5:test": "taro build --type h5 --mode test", "build:h5:live": "taro build --type h5 --mode live", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "test": "jest", "prettier": "npx prettier --write ."}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@taroify/core": "^0.9.0", "@tarojs/components": "3.6.30", "@tarojs/helper": "3.6.30", "@tarojs/plugin-framework-react": "3.6.30", "@tarojs/plugin-platform-alipay": "3.6.30", "@tarojs/plugin-platform-h5": "3.6.30", "@tarojs/plugin-platform-harmony-hybrid": "3.6.30", "@tarojs/plugin-platform-jd": "3.6.30", "@tarojs/plugin-platform-qq": "3.6.30", "@tarojs/plugin-platform-swan": "3.6.30", "@tarojs/plugin-platform-tt": "3.6.30", "@tarojs/plugin-platform-weapp": "3.6.30", "@tarojs/react": "3.6.30", "@tarojs/router": "3.6.30", "@tarojs/runtime": "3.6.30", "@tarojs/shared": "3.6.30", "@tarojs/taro": "3.6.30", "jotai": "^2.12.5", "react": "^18.0.0", "react-dom": "^18.0.0", "react-markdown": "^10.1.0", "react-use": "^17.5.0", "remark-breaks": "^4.0.0", "taro-parse": "^1.1.5"}, "devDependencies": {"@babel/core": "^7.8.0", "@commitlint/cli": "^19.1.0", "@commitlint/config-conventional": "^19.1.0", "@msb-next/webpack-plugins": "latest", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.30", "@tarojs/plugin-html": "3.6.30", "@tarojs/taro-loader": "3.6.30", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.30", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/sass": "1.43.1", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "autoprefixer": "^10.4.18", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "3.6.30", "eslint": "^8.12.0", "eslint-config-taro": "3.6.30", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^9.0.11", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "postcss": "^8.4.35", "postcss-rem-to-responsive-pixel": "^6.0.1", "prettier": "^3.6.2", "react-refresh": "^0.11.0", "remove-files-webpack-plugin": "^1.5.0", "stylelint": "^14.4.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.1.0", "weapp-tailwindcss": "^3.0.11", "webpack": "5.78.0", "webpack-aliyun-oss": "^0.3.13"}, "engines": {"node": ">=18.0.0"}}