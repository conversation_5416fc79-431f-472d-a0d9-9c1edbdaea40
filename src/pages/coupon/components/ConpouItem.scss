.coupon {
  padding: 20px;
  width: 100%;
  box-sizing: border-box;

  .couponItem {
    width: 100%;
    height: 160px;
    display: flex;
    // background-color: #fbefdf;
    // border-radius: 8px;
    background: url('../../../assets/images/coupon/bg.png') no-repeat;
    background-size: 100% 100%;

    .leftItem {
      width: 25%;
      height: 100%;
      position: relative;

      .count {
        position: absolute;
        top: 0;
        left: 0;
        color: #e40633;
        font-size: 20px;
        text-align: center;
        font-weight: 500;
        font-family:
          PingFangSC,
          PingFang SC;
        width: 64px;
        height: 48px;
        background: url('../../../assets/images/coupon/countTitle.png') no-repeat;
        background-size: 100% 100%;
      }

      .content {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        text-align: center;

        .moneyCount {
          margin-top: 30px;
          height: 60px;
          line-height: 60px;
          color: #e60f37;
          font-size: 60px;
          font-weight: 700;
          font-family: LiGothicMed;

          .unit {
            display: inline;
            font-size: 28px;
          }
        }

        .xianzhi {
          font-family:
            PingFangSC,
            PingFang SC;
          font-size: 20px;
          color: #e60f37;
        }
      }
    }
    .rightItem {
      width: 75%;
      padding: 10px 30px 10px;
      box-sizing: border-box;
      position: relative;
      display: flex;
      font-size: 20px;

      .leftContentBox {
        width: 50%;
        font-size: 20px;

        .title {
          padding-top: 10px;
          color: #000000;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #000000;
          line-height: 34px;
        }

        .time {
          color: #e40032;
          margin-bottom: 10px;

          .timeText {
            display: inline;
            color: #696969;
          }
        }

        .useTitle {
          color: #696969;
        }
      }

      .rightBtnBox {
        width: 50%;
        position: relative;

        .btns {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          height: 56px;

          .btn {
            width: 102px;
            height: 52px;
            text-align: center;
            line-height: 56px;

            color: #fff;
            background: linear-gradient(220deg, #ff6a5e 0%, #e40633 100%);
            border-radius: 8px;
          }

          .snedFri {
            color: #e71037;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 20px;
            line-height: 52px;
            text-align: center;
            font-style: normal;
            background: url('../../../assets/images/coupon/btn1.png') no-repeat;
            background-size: 100% 100%;
          }

          .toUse {
            color: #fff;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 20px;
            line-height: 52px;
            text-align: center;
            font-style: normal;
            background: url('../../../assets/images/coupon/btn2.png') no-repeat;
            background-size: 100% 100%;
          }
        }

        .title {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #696969;
        }
      }
    }
  }
}
