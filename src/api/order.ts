import { api1Request } from '@/utils/request'

// 参数接口
export interface OrderListParams {
  /*订单状态（全部不传递此字段，0-待支付，100-已支付（待发货），200-已发货，300-已完成） */
  orderState?: number
  /*页码 */
  pageNum?: number
  /*时间 */
  pageSize?: number
}

// 响应接口
export interface OrderListRes {
  data: {
    pageNum: number
    total: number
    list: {
      id: number
      ctime: number
      utime: number
      del: number
      orderState: number
      payTime: number | null
      productTitle: string
      productDesc: string
      expressId: number
      productImage: string
      size: string | null
      colour: string | null
      styleCode: string | null
      templateCode: string | null
      totalNum: number | null
      totalPrice: number
      tradeNo: string | null
      costDetail: {
        /*总购买数量 */
        totalNum: number
        /*总价格 */
        totalPrice: number
        /*前面工艺ID */
        frontPrintingId: number
        /*前面工艺 */
        frontPrinting: string
        /*前面面积 (单位:平方厘米㎡) */
        frontArea: number
        /*前面价格 */
        frontPrice: number
        /*后面工艺ID */
        backPrintingId: number
        /*后面工艺 */
        backPrinting: string
        /*后面面积 (单位:平方厘米㎡) */
        backArea: number
        /*后面价格 */
        backPrice: number
        /*T恤价格 */
        basePrice: number
      }
    }[]
  }
}

/**
 * 订单列表
 * @param {object} params 订单列表查询参数
 * @param {number} params.orderState 订单状态（全部不传递此字段，0-待支付，100-已支付（待发货），200-已发货，300-已完成）
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 时间
 * @returns
 */
export function getOrderList(params: OrderListParams): Promise<OrderListRes> {
  return api1Request.post({
    url: `/api/order/list`,
    data: params
  })
}

// 参数接口
export interface OrderCreateParams {
  /*openId */
  openId?: string
  /*优惠券ID */
  couponId?: number
  /*设计完成模版ID */
  designTemplateId?: number
  /*尺码 */
  size?: string
  /*计算价格数据 */
  costDetail?: {
    /*总购买数量 */
    totalNum?: number
    /*总价格 */
    totalPrice?: number
    /*前面工艺ID */
    frontPrintingId?: number
    /*前面工艺 */
    frontPrinting?: string
    /*前面面积 (单位:平方厘米㎡) */
    frontArea?: number
    /*前面价格 */
    frontPrice?: number
    /*后面工艺ID */
    backPrintingId?: number
    /*后面工艺 */
    backPrinting?: string
    /*后面面积 (单位:平方厘米㎡) */
    backArea?: number
    /*后面价格 */
    backPrice?: number
  }
  /*地址ID */
  addressId?: number
  /*总价格 */
  totalPrice: number
}

// 响应接口
export interface OrderCreateRes {
  data: string
}

/**
 * 创建订单
 * @param {object} params 创建订单请求参数
 * @param {string} params.openId openId
 * @param {number} params.couponId 优惠券ID
 * @param {number} params.designTemplateId 设计完成模版ID
 * @param {string} params.size 尺码
 * @param {object} params.costDetail 计算价格数据
 * @param {number} params.addressId 地址ID
 * @returns
 */
export function orderCreate(params: OrderCreateParams): Promise<OrderCreateRes> {
  return api1Request.post({
    url: `/api/order/create`,
    data: params
  })
}

// 响应接口
export interface OrderCreatInfoRes {
  data: {
    readyImageList: string[]
    printingImageList: string[]
    userAddress: {
      id: number
      ctime: number
      utime: number
      del: number
      provinceCode: string
      province: string
      cityCode: string
      city: string
      areaCode: string
      area: string
      streetCode: string
      street: string
      detail: string
      userId: number
      contacts: string
      phone: string
    }
    patternSize: {
      id: number
      ctime: number
      del: number
      templateCode: string
      styleCode: string
      size: string
      gender: string
      length: number
      shoulderWidth: number
      bust: number
      legOpening: number
      sleeveLength: number
      cuff: number
    }[]
    price: number
    costDetail: {
      totalNum: number
      totalPrice: number
      frontPrintingId: number
      frontPrinting: string
      frontArea: number
      frontPrice: number
      backPrintingId: number
      backPrinting: string
      backArea: number
      backPrice: number
      sizeCode: string
      basePrice: number
    }
    expressTips: string
    waringTips: string
    diyData: string
    gender: string
    templateCode: string
    styleCode: string
  }
}

/**
 * 获取创建订单信息
 * @param {string} designTemplateId 设置完成模版ID
 * @returns
 */
export function orderCreatInfo(designTemplateId: string): Promise<OrderCreatInfoRes> {
  // return request.get(`/order/create/info?designTemplateId=${designTemplateId}`)
  return api1Request.get({
    url: `/api/order/create/info?designTemplateId=${designTemplateId}`
  })
}

// 响应接口
export interface OrderCancelRes {
  data: number
}

/**
 * 取消订单
 * @param {string} tradeNo
 * @returns
 */
export function orderCancel(tradeNo: string): Promise<OrderCancelRes> {
  return api1Request.post({
    url: `/api/order/cancel`,
    data: { tradeNo },
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 响应接口
export interface OrderConfirmRes {
  data: number
}

/**
 * 确认收货
 * @param {string} tradeNo
 * @returns
 */
export function orderConfirm(tradeNo: string): Promise<OrderConfirmRes> {
  return api1Request.post({
    url: `/api/order/confirm`,
    data: { tradeNo },
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 参数接口
export interface UpdateAddressParams {
  /*创建时间 */
  ctime?: number
  /*创建时间 */
  utime?: number
  /*删除，0-正常，1-删除 */
  del?: number
  /*省 */
  provinceCode?: string
  /*省 */
  province?: string
  /*市 */
  cityCode?: string
  /*市 */
  city?: string
  /*区 */
  areaCode?: string
  /*区 */
  area?: string
  /*街道ID */
  streetCode?: string
  /*街道 */
  street?: string
  /*详情 */
  detail?: string
  /*用户ID */
  userId?: number
  /*联系人 */
  contacts?: string
  /*联系人-电话 */
  phone?: string
}

// 响应接口
export interface UpdateAddressRes {}

/**
 * 更新物流地址
 * @param {string} orderId 物流ID
 * @param {object} params 用户地址
 * @param {number} params.ctime 创建时间
 * @param {number} params.utime 创建时间
 * @param {number} params.del 删除，0-正常，1-删除
 * @param {string} params.provinceCode 省
 * @param {string} params.province 省
 * @param {string} params.cityCode 市
 * @param {string} params.city 市
 * @param {string} params.areaCode 区
 * @param {string} params.area 区
 * @param {string} params.streetCode 街道ID
 * @param {string} params.street 街道
 * @param {string} params.detail 详情
 * @param {number} params.userId 用户ID
 * @param {string} params.contacts 联系人
 * @param {string} params.phone 联系人-电话
 * @returns
 */
export function updateAddress(orderId: string, params: UpdateAddressParams): Promise<UpdateAddressRes> {
  return api1Request.post({
    url: `/api/express/update/address/${orderId}`,
    data: params
  })
}

interface TrackingDataItem {
  time: string
  context: string
  ftime: string
  areaCode: null
  areaName: null
  status: null
  areaCenter: null
  areaPinYin: null
  statusCode: null
}

interface ExpressTrackingInfo {
  receiver: string
  receiverMobile: string
  province: string
  city: string
  area: string
  street: string
  address: string
  outsideExpressNo: string
  outsideExpressCompany: string
  nu: string
  ischeck: string
  com: string
  data: TrackingDataItem[]
  state: string
  condition: string
}

// 响应接口
export interface QueryExpressTrackRes {
  data: ExpressTrackingInfo
}

/**
 * 物流详情
 * @param {string} expressId 物流ID
 * @returns
 */
export function queryExpressTrack(expressId: string): Promise<QueryExpressTrackRes> {
  return api1Request.get({
    url: `/api/express/track/detail?expressId=${expressId}`
  })
}
