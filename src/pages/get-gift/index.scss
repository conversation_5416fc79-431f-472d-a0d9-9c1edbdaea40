.bg {
  width: 100%;
  height: 100vh;
  background: url('../../assets/images/getgift/bg.png') no-repeat;
  background-size: 100% 100%;

  .dialog {
    padding-left: 38px;
    padding-right: 38px;
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 654px;
    height: 590px;
    background: url('../../assets/images/getgift/dialog.png') no-repeat;
    background-size: 100% 100%;

    .dialogTitle {
      margin-top: 54px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 50px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 56px;
      text-align: left;
      font-style: normal;
    }

    .time {
      margin-top: 8px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #7c2303;
      line-height: 40px;
      text-align: left;
      font-style: normal;
      margin-bottom: 56px;
    }

    .btn {
      width: 542px;
      height: 100px;
      margin: 64px auto;
      background: linear-gradient(220deg, #ff6a5e 0%, #e40633 100%);
      border-radius: 28px;
      text-align: center;
      line-height: 100px;
      color: #fff;
    }

    .geted {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #e40032;
      line-height: 44px;
      text-align: left;
      font-style: normal;
      margin-top: 94px;
      text-align: center;
    }
  }
}

.shadowBg {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

.dialogNoupou {
  width: 624px;
  height: 548px;
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 999;
  transform: translate(-50%, -50%);
  background: url('../../assets/images/index/dialogBg.png') no-repeat;
  background-size: 100% 100%;

  padding: 56px 28px 0 28px;
  box-sizing: border-box;

  .hint {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 40px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 56px;
    text-align: left;
    font-style: normal;
  }

  .use {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 28px;
    color: #7c2303;
    line-height: 40px;
    text-align: left;
    font-style: normal;
  }

  .noupon {
    margin-top: 42px;
    width: 100%;
    height: 160px;
    background: url('../../assets/images/index/conpouBg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;

    .leftItem {
      width: 30%;
      height: 100%;
      position: relative;

      .count {
        position: absolute;
        top: 0;
        left: 0;
        color: #e40633;
        font-size: 20px;
        text-align: center;
        font-weight: 500;
        font-family:
          PingFangSC,
          PingFang SC;
        width: 64px;
        height: 48px;
        background: url('../../assets/images/coupon/countTitle.png') no-repeat;
        background-size: 100% 100%;
      }

      .content {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        text-align: center;

        .moneyCount {
          margin-top: 30px;
          height: 60px;
          line-height: 60px;
          font-size: 60px;
          font-weight: 700;
          font-family: LiGothicMed;
          color: #e60f37;

          .unit {
            display: inline;
            font-size: 28px;
          }
        }

        .xianzhi {
          font-family:
            PingFangSC,
            PingFang SC;
          font-size: 20px;
          color: #e60f37;
        }
      }
    }
    .rightItem {
      width: 70%;
      padding: 22px 0 0 30px;
      box-sizing: border-box;
      position: relative;
      font-size: 20px;

      .title {
        padding-top: 10px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 34px;
      }

      .time {
        color: #e40032;
        margin-bottom: 10px;

        .timeText {
          display: inline;
          color: #ccc;
        }
      }

      .useTitle {
        color: #ccc;
      }
    }
  }

  .btn {
    margin: 52px auto;
    width: 518px;
    height: 96px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 40px;
    color: #fffffa;
    line-height: 96px;
    text-align: center;
    font-style: normal;
    background: url('../../assets/images/index/btnBG.png') no-repeat;
    background-size: 100% 100%;
    margin-bottom: 86px;
  }

  .closeBtn {
    margin: 0 auto;
    width: 46px;
    height: 46px;
    border-radius: 50%;
    border: 4px solid #ffffff;
    box-sizing: border-box;
    color: #fff;
    text-align: center;
    line-height: 36px;
    font-size: 20px;
    font-weight: 700;
  }
}
