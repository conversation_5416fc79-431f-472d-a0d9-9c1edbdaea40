import { Button, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import Slice67Img from '@/assets/images/index/slice-67.png'
import Slice68Img from '@/assets/images/index/slice-68.png'
import Slice69Img from '@/assets/images/index/slice-69.png'
import Slice70Img from '@/assets/images/index/slice-70.png'
import Slice71Img from '@/assets/images/index/slice-71.png'
import Slice72Img from '@/assets/images/index/slice-72.png'
import Slice73Img from '@/assets/images/index/slice-73.png'
import Slice74Img from '@/assets/images/index/slice-74.png'
import Slice75Img from '@/assets/images/index/slice-75.png'
import Slice76Img from '@/assets/images/index/slice-76.png'
import process1Img from '@/assets/images/index/pattern-craft.png'
import process2Img from '@/assets/images/index/online-production.png'
import process3Img from '@/assets/images/index/ai-designer.png'
import Slice83Img from '@/assets/images/index/slice-83.png'
import useObjState from '@/hooks/useObjState'
import BottomInput from '@/components/BottomInput'
import useObjAtom from '@/hooks/useObjAtom'
import { activePathState, userinfoState } from '@/store/global'
import { phoneAuth } from '@/utils'
import { useEffect, useState } from 'react'
import HorizontalScrollRecommend from '@/components/HorizontalScrollRecommend'
import NavBarTitle from './components/NavBarTitle'
import Drawer from './components/Drawer'
import './index.scss'

const Index = () => {
  const userinfo = useObjAtom(userinfoState)
  const activePath = useObjAtom(activePathState)
  const list = [1, 2]
  const keyboardHeight = useObjState(0) // 键盘高度

  // 授权手机号
  const getPhoneNumber = async (val) => {
    const { detail } = val
    if (detail.code) {
      const res = await phoneAuth(detail)
      if (res.code === 200) {
        userinfo.set((v) => {
          if (v) {
            return {
              ...v,
              phone: res.data
            }
          }
          return null
        })
      } else {
        Taro.showToast({
          title: `授权失败: ${res.msg}`,
          icon: 'none',
          duration: 2000
        })
      }
    } else {
      console.log('取消授权')
    }
  }

  const recommendList = useObjState([
    {
      img: Slice83Img,
      title: '照片转漫画风 T 恤',
      desc: '将照片转换为漫画风格的 T 恤设计'
    },
    {
      img: Slice83Img,
      title: '恶搞世界名画创意 T 恤',
      desc: '将名画恶搞成 T 恤设计'
    },
    {
      img: Slice83Img,
      title: '风景文旅艺术 T 恤',
      desc: '将风景文旅艺术化为 T 恤设计'
    },
    {
      img: Slice83Img,
      title: '人物肖像定制 T 恤',
      desc: '将人物肖像定制化为 T 恤设计'
    },
    {
      img: Slice83Img,
      title: '动物萌宠可爱 T 恤',
      desc: '将动物萌宠可爱化为 T 恤设计'
    },
    {
      img: Slice83Img,
      title: '卡通动漫角色 T 恤',
      desc: '将卡通动漫角色设计为 T 恤'
    },
    {
      img: Slice83Img,
      title: '抽象艺术风格 T 恤',
      desc: '将抽象艺术风格设计为 T 恤'
    }
  ])

  const [getGiftVisible, setGiftVisible] = useState(false)
  const [notGetGiftVisible, setNotGetGiftVisible] = useState(false)

  const sendMessage = (text, type) => {
    Taro.setStorage({
      key: 'chatMessageData',
      data: { text, type }
    }).then(() => {
      Taro.navigateTo({
        url: '/pages/chat/index'
      })
    })
  }

  // 监听键盘高度变化
  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const onKeyboardHeightChange = (res: { height: number }) => {
        console.log('res.height', res.height)
        keyboardHeight.set(res.height)
      }

      Taro.onKeyboardHeightChange(onKeyboardHeightChange)

      return () => {
        console.log('first', first)
        Taro.offKeyboardHeightChange(onKeyboardHeightChange)
      }
    }
  }, [])

  return (
    <>
      <div className="w-full h-screen bg-gradient-light overflow-hidden flex flex-col">
        <NavBarTitle />
        <div className="relative flex-1 overflow-auto pb-[172px]">
          <div className="absolute top-[20px] h-[550px] w-full flex_center z-0">
            <Image className="w-[480px] h-[550px] ml-[46px]" src={Slice68Img} />
          </div>
          <div className="w-full z-10 relative">
            <div className="flex_center">
              <Image className="w-[96px] h-[96px] mt-[48px]" src={Slice67Img} />
            </div>
            <div className="flex_center h-[66px] font-extrabold text-[48px] text-[#303030] text-left not-italic normal-case mt-[24px]">
              Hi, Can I Help You ?
            </div>
            <div className="mt-[6px] flex_center flex-col w-full h-[68px] font-normal text-[24px] text-[#666666] leading-[34px] text-center not-italic normal-case">
              <div>我是你的T恤专属设计师，我可以帮你</div>
              <div>在【行走的画布】上完成设计，释放创造力！</div>
            </div>

            {/* 横向滚动推荐列表组件 */}
            {/* <HorizontalScrollRecommend
              list={recommendList.val}
              scrollSpeed={30}
              autoScroll
              onItemClick={(item, index) => {
                console.log('点击了推荐项:', item.title, index)
                // 这里可以添加点击事件处理逻辑
              }}
            /> */}

            <div className="h-[250px] px-[40px] flex justify-between items-end">
              <div className="w-[328px] h-[202px] relative">
                {!userinfo.val?.phone && (
                  <Button
                    openType="getPhoneNumber"
                    onGetPhoneNumber={getPhoneNumber}
                    className="absolute left-0 top-0 z-[100] leading-[inherit] m-0 p-0 rounded-none border-[none] bg-transparent w-full h-full hide-after"
                  ></Button>
                )}
                <Image
                  className="animate-[float_3s_ease-in-out_infinite] absolute z-10 top-[-34px] right-[-2px] w-[112px] h-[112px]"
                  src={Slice69Img}
                />
                <Image className="absolute z-0 top-0 left-0 w-full h-full" src={Slice70Img} />
                <div className="z-10 relative pl-[24px]">
                  <div className="font-semibold text-[32px] text-[#311306] leading-[48px] text-left not-italic normal-case mt-[24px]">
                    AI定制设计
                  </div>
                  <div className="font-normal text-[24px] text-[#6E574D] leading-[32px] text-left not-italic normal-case mt-[2px]">
                    语音创作,可视化编辑
                  </div>
                  <div
                    onClick={() => {
                      Taro.navigateTo({
                        url: '/pages/canvas/index'
                      })
                    }}
                    className="w-[152px] h-[48px] shadow-[0px_8_16px_0px_rgba(255,106,106,0.3)] rounded-[8px] mt-[24px] bg-gradient-red flex_center mr-[4px]"
                  >
                    <div className="font-medium text-[24px] text-white leading-[32px] text-left not-italic normal-case">立即定制</div>
                    <Image className="w-[24px] h-[24px]" src={Slice71Img} />
                  </div>
                </div>
              </div>
              <div className="w-[328px] h-[202px] relative">
                {!userinfo.val?.phone && (
                  <Button
                    openType="getPhoneNumber"
                    onGetPhoneNumber={getPhoneNumber}
                    className="absolute left-0 top-0 z-[100] leading-[inherit] m-0 p-0 rounded-none border-[none] bg-transparent w-full h-full hide-after"
                  ></Button>
                )}
                <Image
                  className="animate-[float_3s_ease-in-out_infinite] absolute z-10 top-[-48px] right-[-36px] w-[172px] h-[172px]"
                  src={Slice72Img}
                />
                <Image className="absolute top-0 left-0 w-full h-full" src={Slice73Img} />
                <div className="z-10 relative pl-[24px]">
                  <div className="font-semibold text-[32px] text-[#311306] leading-[48px] text-left not-italic normal-case mt-[24px]">
                    灵感成衣
                  </div>
                  <div className="font-normal text-[24px] text-[#6E574D] leading-[32px] text-left not-italic normal-case mt-[2px]">
                    设计师高定
                  </div>
                  <div
                    onClick={() => {
                      Taro.switchTab({
                        url: '/pages/inspiration/index',
                        success: () => {
                          activePath.set('/pages/inspiration/index')
                        }
                      })
                    }}
                    className="w-[152px] h-[48px] shadow-[0px_8_16px_0px_rgba(255,106,106,0.3)] rounded-[8px] mt-[24px] bg-gradient-blue flex_center mr-[4px]"
                  >
                    <div className="font-medium text-[24px] text-white leading-[32px] text-left not-italic normal-case">立即选购</div>
                    <Image className="w-[24px] h-[24px]" src={Slice71Img} />
                  </div>
                </div>
              </div>
            </div>

            <div className="px-[40px] h-[202px] mt-[24px]">
              <div
                className="w-full h-full relative"
                onClick={() => {
                  Taro.navigateTo({
                    url: '/pages/poster/index'
                  })
                }}
              >
                <Image
                  className="animate-[float_3s_ease-in-out_infinite] absolute z-10 top-[-42px] right-[-32px] w-[200px] h-[200px]"
                  src={Slice74Img}
                />
                <Image className="absolute top-0 left-0 w-full h-full" src={Slice75Img} />
                <div className="z-10 relative flex flex-col">
                  <div className="font-semibold text-[32px] text-[#311306] leading-[48px] text-left not-italic normal-case mt-[24px] px-[24px]">
                    一件起订，极速出货
                  </div>
                  <div className="mt-[10px] px-[24px] flex justify-between items-center">
                    <Image className=" w-[192px] h-[104px]" src={process1Img} />
                    <Image className="w-[24px] h-[24px]" src={Slice76Img} />
                    <Image className=" w-[192px] h-[104px]" src={process2Img} />
                    <Image className="w-[24px] h-[24px]" src={Slice76Img} />
                    <Image className=" w-[192px] h-[104px]" src={process3Img} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="h-[154px]"></div>
      </div>
      <div
        className="fixed z-50 left-0 w-full h-[112px] transition-all duration-300 ease-in-out"
        style={{ bottom: keyboardHeight.val > 0 ? `${keyboardHeight.val + 34}px` : '184rpx' }}
      >
        {!userinfo.val?.phone && (
          <Button
            openType="getPhoneNumber"
            onGetPhoneNumber={getPhoneNumber}
            className="absolute left-0 top-0 z-[100] leading-[inherit] m-0 p-0 rounded-none border-[none] bg-transparent w-full h-full hide-after"
          ></Button>
        )}
        <BottomInput sendMessage={sendMessage} />
      </div>
      {userinfo.val && <Drawer />}

      {getGiftVisible && (
        <div className="shadowBg">
          <div className="dialogNoupou">
            <div className="hint">代金券即将过期</div>
            <div className="use">请及时使用</div>
            <div className="noupon">
              <div className="leftItem">
                <div className="content">
                  <div className="moneyCount">
                    3<span className="unit">元</span>
                  </div>
                  <div className="xianzhi">无门槛</div>
                </div>
              </div>
              <div className="rightItem">
                <div className="title">无门槛代金券</div>
                <div className="time">
                  23 : 59 : 59 <span className="timeText">后过期</span>
                </div>
                <div className="useTitle">全店可用</div>
              </div>
            </div>
            <div
              className="btn"
              onClick={() => {
                Taro.switchTab({
                  url: '/pages/inspiration/index',
                  success: () => {
                    activePath.set('/pages/inspiration/index')
                  }
                })
              }}
            >
              去使用
            </div>

            <div className="closeBtn" onClick={() => setGiftVisible(false)}>
              ×
            </div>
          </div>
        </div>
      )}

      {notGetGiftVisible && (
        <div className="shadowBg">
          <div className="dialogNoupou-notGet">
            <div className="moneyValueIcon"></div>
            <div className="coloured"></div>
            <div className="hint">200元代金券</div>
            <div className="use">限时满减+无门槛代金券</div>
            {list.map((item) => (
              <div className="noupon" key={item}>
                <div className="leftItem">
                  <div className="content">
                    <div className="moneyCount">
                      3<span className="unit">元</span>
                    </div>
                    <div className="xianzhi">无门槛</div>
                  </div>
                </div>
                <div className="rightItem">
                  <div className="leftContentBox">
                    <div className="title">无门槛代金券</div>
                    <div className="time">
                      23 : 59 : 59 <span className="timeText">后过期</span>
                    </div>
                    <div className="useTitle">全店可用</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="juxing">
            <div className="juxing_btn">一键领取</div>
            <div className="closeBtn_dialog" onClick={() => setNotGetGiftVisible(false)}>
              ×
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default Index
