.useCouponItem {
  width: 100%;
  height: 160px;
  display: flex;
  // background-color: #fbefdf;
  // border-radius: 8px;
  background: url('../../../assets/images/coupon/bg1.png') no-repeat;
  background-size: 100% 100%;

  .leftItem {
    width: 25%;
    height: 100%;
    position: relative;

    .content {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      text-align: center;

      .moneyCount {
        margin-top: 30px;
        height: 60px;
        line-height: 60px;
        font-size: 60px;
        font-weight: 700;
        font-family: LiGothicMed;

        .unit {
          display: inline;
          font-size: 28px;
        }
      }

      .xianzhi {
        font-family:
          PingFangSC,
          PingFang SC;
        font-size: 20px;
      }
    }
  }
  .rightItem {
    width: 75%;
    padding: 10px 30px 10px;
    box-sizing: border-box;
    position: relative;
    display: flex;
    font-size: 20px;

    .leftContentBox {
      width: 100%;
      font-size: 20px;

      .title {
        padding-top: 10px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 34px;
      }

      .time {
        color: #ccc;
        margin-bottom: 10px;
        font-size: 20px;
      }

      .useTitle {
        color: #ccc;
      }
    }
  }
}
