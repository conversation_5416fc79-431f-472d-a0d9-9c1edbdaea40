import { receiveList } from '../../api/coupon'
import { useAsyncFn } from 'react-use'
import { useEffect } from 'react'
import useObjState from '@/hooks/useObjState'
import ConpouItem from './components/ConpouItem'
import Taro from '@tarojs/taro'
import './index.scss'

const Index = () => {
  const activeProductGroupId = useObjState(1)
  const isOpen = useObjState({})
  const pageNum = useObjState('1')
  const pageSize = useObjState('10')
  const list = [1, 2, 3, 4, 5]

  const [getOrderListState, getOrderListFetch] = useAsyncFn(async () => {
    const params: { pageNum: string; pageSize: string } = {
      pageNum: pageNum.val,
      pageSize: pageSize.val
    }
    const res = await receiveList(params.pageNum, params.pageSize)
    console.log('response', res)
    return res
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  useEffect(() => {
    getOrderListFetch()
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  return (
    <>
      {list.map((item) => (
        <ConpouItem key={item}></ConpouItem>
      ))}

      <div
        className="checkNoUseCoupon"
        onClick={() =>
          Taro.navigateTo({
            url: '/pages/noUseCoupon/index'
          })
        }
      >
        查看无效券
      </div>
    </>
  )
}

export default Index
