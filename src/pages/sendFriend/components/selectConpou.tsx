import './selectConpou.scss'

const Index = (props) => {
  return (
    <>
      <div className="selectBg">
        <div className="selectDialog">
          <div className="header">
            <div className="title">选择代金券</div>
            <div className="closeBtn" onClick={() => props.updateVisible(false)}>
              ×
            </div>
          </div>
          <div className="body">
            <div className="couponItem">
              <div className="leftItem">
                <div className="count">x200</div>
                <div className="content">
                  <div className="moneyCount">
                    3<span className="unit">元</span>
                  </div>
                  <div className="xianzhi">无门槛</div>
                </div>
              </div>
              <div className="rightItem">
                <div className="leftContentBox">
                  <div className="title">无门槛代金券</div>
                  <div className="time">
                    23 : 59 : 59 <span className="timeText">后过期</span>
                  </div>
                  <div className="useTitle">全店可用</div>
                </div>
                <div className="rightBtnBox">
                  <div className="btns">
                    <div className="btn toUse">选择</div>
                  </div>
                  {/* <div className="text">已选择</div> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Index
