import ConputItem from './components/ConpouItem'
import UseConputItem from './components/UseConpouItem'
import useObjAtom from '@/hooks/useObjAtom'
import { activePathState } from '@/store/global'
import { useState } from 'react'
import Taro from '@tarojs/taro'
import './index.scss'

const Index = () => {
  const activePath = useObjAtom(activePathState)
  // 是否领取优惠券
  const [isGet, setGetVisible] = useState(false)
  // 优惠券领取成功弹窗
  const [successVisible, setSuccessVisible] = useState(false)
  return (
    <>
      <div className="bg">
        <div className="dialog">
          <div className="dialogTitle">好友礼赠</div>
          <div className="time">吉吉赠送于7月21日10:10</div>
          {!isGet && <ConputItem></ConputItem>}
          {!isGet && <div className="btn">立即领取</div>}
          {isGet && <UseConputItem></UseConputItem>}
          {isGet && <div className="geted">代金券已被领完</div>}
        </div>

        {successVisible && (
          <div className="shadowBg">
            <div className="dialogNoupou">
              <div className="hint">代金券领取成功</div>
              <div className="use">已放入「我的-优惠券」中</div>
              <div className="noupon">
                <div className="leftItem">
                  <div className="count">x200</div>
                  <div className="content">
                    <div className="moneyCount">
                      3<span className="unit">元</span>
                    </div>
                    <div className="xianzhi">无门槛</div>
                  </div>
                </div>
                <div className="rightItem">
                  <div className="title">无门槛代金券</div>
                  <div className="time">
                    23 : 59 : 59 <span className="timeText">后过期</span>
                  </div>
                  <div className="useTitle">全店可用</div>
                </div>
              </div>
              <div
                className="btn"
                onClick={() => {
                  Taro.switchTab({
                    url: '/pages/inspiration/index',
                    success: () => {
                      activePath.set('/pages/inspiration/index')
                    }
                  })
                }}
              >
                去使用
              </div>

              <div className="closeBtn" onClick={() => setSuccessVisible(false)}>
                ×
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default Index
