import { Popup, Input, Radio } from '@taroify/core'
import { useState } from 'react'
import SelectConpou from './components/selectConpou'
import Taro from '@tarojs/taro'
import './index.scss'

const Index = () => {
  const [open, setOpen] = useState(false)
  const [value, setValue] = useState('')
  return (
    <>
      <div className="continer">
        <div className="noupon">
          <div className="toptitle">
            <div className="daijinquan">代金券</div>
            <div className="change" onClick={() => setOpen(true)}>
              更换
            </div>
          </div>
          <div className="couponItem">
            <div className="leftItem">
              <div className="count">x200</div>
              <div className="content">
                <div className="moneyCount">
                  3<span className="unit">元</span>
                </div>
                <div className="xianzhi">无门槛</div>
              </div>
            </div>
            <div className="rightItem">
              <div className="leftContentBox">
                <div className="title">无门槛代金券</div>
                <div className="time">
                  23 : 59 : 59 <span className="timeText">后过期</span>
                </div>
                <div className="useTitle">全店可用</div>
              </div>
            </div>
          </div>
        </div>
        <div className="snedCount">
          <div className="snedAllCount">赠送代金券总数量</div>
          <div className="countInput">
            <Input
              placeholder=""
              className="inputComponent"
              style={{ fontSize: '20px' }}
              value={value}
              onChange={(e) => setValue(e.detail.value)}
            />
            <span>张</span>
          </div>
        </div>
        <div className="setting">
          <div className="title">领取设置</div>
          <Radio.Group defaultValue="1">
            <Radio name="1">限每人领取1张</Radio>
            <Radio name="2">每人领取多张</Radio>
          </Radio.Group>
        </div>
        <div className="advice">
          <div className="adviceTitle">礼赠留言</div>
          <div className="adviceInput">
            <Input
              placeholder="请输入"
              className="inputComponent"
              style={{ fontSize: '20px' }}
              value={value}
              onChange={(e) => setValue(e.detail.value)}
            />
          </div>
        </div>
        <div className="beizhu">注：赠送后无法取消，请谨慎操作</div>
        <div
          className="sendBtn"
          onClick={() => {
            Taro.navigateTo({
              url: '/pages/get-gift/index'
            })
          }}
        >
          立即赠送
        </div>
      </div>

      <Popup open={open} placement="bottom" style={{ height: '70%' }}>
        <SelectConpou updateVisible={() => setOpen(false)}></SelectConpou>
      </Popup>
    </>
  )
}

export default Index
