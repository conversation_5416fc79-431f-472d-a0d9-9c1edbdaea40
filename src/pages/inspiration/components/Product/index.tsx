import Taro from '@tarojs/taro'

const Product = ({ item }) => {
  return (
    <div
      key={item.id}
      onClick={() => {
        Taro.navigateTo({
          url: `/pages/detail/index?id=${item.id}`
        })
      }}
      className="w-[350px] h-[470px] rounded-[16px] overflow-hidden bg-white flex flex-col"
    >
      <img className="w-[350px] h-[350px] rounded-[16px_16px_0px_0px]" src={item.imageUrl} alt={item.title} />
      <div className="flex-1 flex flex-col justify-center px-[14px]">
        <div className="font-normal text-[28px] text-black leading-[40px] not-italic mb-[8px]">{item.title}</div>
        <div className="flex justify-between items-end">
          <div className="font-normal text-[20px] text-black leading-[24px] not-italic flex items-end">
            ￥<span className="text-[32px] text-black leading-[32px] not-italic">{item.price / 100}</span>起
          </div>
          <div className="font-normal text-[20px] text-black opacity-50 leading-[24px] text-center not-italic">{item.customNum}+人定制</div>
        </div>
      </div>
    </div>
  )
}

export default Product
