import { useEffect, useRef } from 'react'
import { readyTemplateSave } from '@/api/detail'
import { useAsyncFn } from 'react-use'
import Taro, { useRouter } from '@tarojs/taro'
import NavBarTitle from './components/NavBarTitle'
import Detail from './components/Detail'
import Goods, { GoodsRef } from './components/Goods'
import Recommend from './components/Recommend'
import SubmitBtn from './components/SubmitBtn'

const Index = () => {
  const router = useRouter()
  const goodsRef = useRef<GoodsRef>(null)

  const [readyTemplateSaveState, readyTemplateSaveFetch] = useAsyncFn(async () => {
    console.log(router.params.id)
    if (!router.params.id) return
    const res = await readyTemplateSave(router.params.id)
    console.log('response', res)
    return res.data
  }, [])

  useEffect(() => {
    readyTemplateSaveFetch()
  }, [])

  const onCreateOrder = async () => {
    if (goodsRef.current) {
      const result = await goodsRef.current.templateSaveFetch()
      Taro.navigateTo({
        url: '/pages/pay/index?id=' + result.data
      })
    }
  }

  return (
    <>
      <div className="w-full h-screen overflow-hidden flex flex-col bg-[#F8F8F8]">
        <NavBarTitle />
        <div className="relative flex-1 overflow-auto bg-[#F8F8F8]">
          {router.params.id && readyTemplateSaveState.value && (
            <Goods ref={goodsRef} id={router.params.id} data={readyTemplateSaveState.value} />
          )}
          <Detail />
          {/* <Recommend /> */}
        </div>
        {router.params.id && readyTemplateSaveState.value && (
          <SubmitBtn data={readyTemplateSaveState.value} onCreateOrder={onCreateOrder} />
        )}
      </div>
    </>
  )
}

export default Index
